<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NTag, NProgress, NSpace, NIcon } from 'naive-ui';
import { Icon } from '@iconify/vue';

interface Props {
  healthData?: Api.Monitor.AppHealthData | null;
  businessData?: Api.Monitor.BusinessStatsData | null;
  performanceData?: Api.Monitor.PerformanceData | null;
  systemData?: Api.Monitor.SystemOverviewData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  healthData: null,
  businessData: null,
  performanceData: null,
  systemData: null,
  loading: false
});

// 计算关键指标
const keyMetrics = computed(() => {
  const metrics = {
    systemStatus: 'unknown',
    totalTasks: 0,
    runningTasks: 0,
    successRate: 0,
    avgResponseTime: 0,
    cpuUsage: 0,
    memoryUsage: 0,
    errorRate: 0
  };

  if (props.healthData) {
    metrics.systemStatus = props.healthData.app_info.status;
  }

  if (props.businessData) {
    metrics.totalTasks = props.businessData.strm_tasks.total;
    metrics.runningTasks = props.businessData.strm_tasks.running;
    metrics.successRate = props.businessData.strm_tasks.success_rate * 100;
  }

  if (props.performanceData) {
    metrics.avgResponseTime = props.performanceData.api_performance.avg_response_time;
    metrics.errorRate = props.performanceData.api_performance.error_rate * 100;
  }

  if (props.systemData?.system_resources.available) {
    metrics.cpuUsage = props.systemData.system_resources.cpu_usage || 0;
    metrics.memoryUsage = props.systemData.system_resources.memory_usage?.percent || 0;
  }

  return metrics;
});

// 获取状态颜色
function getStatusColor(status: string) {
  switch (status) {
    case 'healthy':
    case 'running':
      return '#52c41a';
    case 'warning':
      return '#faad14';
    case 'error':
    case 'unhealthy':
      return '#ff4d4f';
    default:
      return '#d9d9d9';
  }
}

// 获取使用率状态
function getUsageStatus(usage: number) {
  if (usage <= 60) return { type: 'success', color: '#52c41a' };
  if (usage <= 80) return { type: 'warning', color: '#faad14' };
  return { type: 'error', color: '#ff4d4f' };
}
</script>

<template>
  <NCard class="realtime-metrics-card">
    <template #header>
      <div class="flex items-center gap-3">
        <Icon icon="mdi:chart-line" class="text-2xl text-blue-500" />
        <span class="text-lg font-semibold">实时监控概览</span>
        <NTag type="success" size="small" class="ml-auto">
          <Icon icon="mdi:circle" class="mr-1 animate-pulse" />
          实时更新
        </NTag>
      </div>
    </template>

    <NGrid :cols="24" :x-gap="16" :y-gap="16">
      <!-- 系统状态 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:server" class="text-2xl" :style="{ color: getStatusColor(keyMetrics.systemStatus) }" />
          </div>
          <div class="metric-content">
            <div class="metric-label">系统状态</div>
            <div class="metric-value">
              <NTag :type="keyMetrics.systemStatus === 'healthy' ? 'success' : 'warning'" size="large">
                {{ keyMetrics.systemStatus === 'healthy' ? '正常' : '异常' }}
              </NTag>
            </div>
          </div>
        </div>
      </NGridItem>

      <!-- 任务统计 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:format-list-checks" class="text-2xl text-blue-500" />
          </div>
          <div class="metric-content">
            <div class="metric-label">任务总数</div>
            <div class="metric-value">
              <span class="text-2xl font-bold">{{ keyMetrics.totalTasks }}</span>
              <span class="text-sm text-gray-500 ml-2">运行中: {{ keyMetrics.runningTasks }}</span>
            </div>
          </div>
        </div>
      </NGridItem>

      <!-- 成功率 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:check-circle" class="text-2xl text-green-500" />
          </div>
          <div class="metric-content">
            <div class="metric-label">成功率</div>
            <div class="metric-value">
              <span class="text-2xl font-bold">{{ keyMetrics.successRate.toFixed(1) }}%</span>
              <NProgress
                :percentage="keyMetrics.successRate"
                :color="getUsageStatus(100 - keyMetrics.successRate).color"
                :show-indicator="false"
                class="mt-1"
              />
            </div>
          </div>
        </div>
      </NGridItem>

      <!-- 响应时间 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:speedometer" class="text-2xl text-purple-500" />
          </div>
          <div class="metric-content">
            <div class="metric-label">平均响应时间</div>
            <div class="metric-value">
              <span class="text-2xl font-bold">{{ keyMetrics.avgResponseTime.toFixed(2) }}s</span>
              <NTag 
                :type="keyMetrics.avgResponseTime <= 1 ? 'success' : keyMetrics.avgResponseTime <= 3 ? 'warning' : 'error'" 
                size="small" 
                class="ml-2"
              >
                {{ keyMetrics.avgResponseTime <= 1 ? '优秀' : keyMetrics.avgResponseTime <= 3 ? '良好' : '较慢' }}
              </NTag>
            </div>
          </div>
        </div>
      </NGridItem>

      <!-- CPU使用率 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:cpu-64-bit" class="text-2xl text-orange-500" />
          </div>
          <div class="metric-content">
            <div class="metric-label">CPU使用率</div>
            <div class="metric-value">
              <span class="text-2xl font-bold">{{ keyMetrics.cpuUsage.toFixed(1) }}%</span>
              <NProgress
                :percentage="keyMetrics.cpuUsage"
                :color="getUsageStatus(keyMetrics.cpuUsage).color"
                :show-indicator="false"
                class="mt-1"
              />
            </div>
          </div>
        </div>
      </NGridItem>

      <!-- 内存使用率 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:memory" class="text-2xl text-cyan-500" />
          </div>
          <div class="metric-content">
            <div class="metric-label">内存使用率</div>
            <div class="metric-value">
              <span class="text-2xl font-bold">{{ keyMetrics.memoryUsage.toFixed(1) }}%</span>
              <NProgress
                :percentage="keyMetrics.memoryUsage"
                :color="getUsageStatus(keyMetrics.memoryUsage).color"
                :show-indicator="false"
                class="mt-1"
              />
            </div>
          </div>
        </div>
      </NGridItem>

      <!-- 错误率 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:alert-circle" class="text-2xl text-red-500" />
          </div>
          <div class="metric-content">
            <div class="metric-label">错误率</div>
            <div class="metric-value">
              <span class="text-2xl font-bold">{{ keyMetrics.errorRate.toFixed(2) }}%</span>
              <NTag 
                :type="keyMetrics.errorRate <= 1 ? 'success' : keyMetrics.errorRate <= 5 ? 'warning' : 'error'" 
                size="small" 
                class="ml-2"
              >
                {{ keyMetrics.errorRate <= 1 ? '正常' : keyMetrics.errorRate <= 5 ? '注意' : '异常' }}
              </NTag>
            </div>
          </div>
        </div>
      </NGridItem>

      <!-- 在线状态 -->
      <NGridItem :span="24" :sm-span="12" :md-span="6">
        <div class="metric-item">
          <div class="metric-icon">
            <Icon icon="mdi:wifi" class="text-2xl text-green-500" />
          </div>
          <div class="metric-content">
            <div class="metric-label">连接状态</div>
            <div class="metric-value">
              <NTag type="success" size="large">
                <Icon icon="mdi:check-circle" class="mr-1" />
                在线
              </NTag>
            </div>
          </div>
        </div>
      </NGridItem>
    </NGrid>
  </NCard>
</template>

<style scoped>
.realtime-metrics-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.realtime-metrics-card :deep(.n-card-header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.realtime-metrics-card :deep(.n-card__content) {
  background: rgba(255, 255, 255, 0.05);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  height: 100%;
}

.metric-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.metric-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-label {
  font-size: 0.875rem;
  opacity: 0.8;
  margin-bottom: 4px;
}

.metric-value {
  font-weight: 600;
}

.metric-value .text-2xl {
  color: white;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}
</style>
