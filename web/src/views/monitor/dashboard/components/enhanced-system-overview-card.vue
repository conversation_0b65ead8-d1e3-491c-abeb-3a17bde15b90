<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { NCard, NStatistic, NTag, NButton, NSpace, NIcon, NGrid, NGridItem, NProgress } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useEcharts } from '@/hooks/common/echarts';

interface Props {
  healthData?: Api.Monitor.AppHealthData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  healthData: null,
  loading: false
});

const router = useRouter();

// 健康状态仪表盘
const { domRef: healthGaugeRef, updateOptions: updateHealthGauge } = useEcharts(() => ({
  series: [{
    type: 'gauge',
    startAngle: 180,
    endAngle: 0,
    center: ['50%', '75%'],
    radius: '90%',
    min: 0,
    max: 100,
    splitNumber: 8,
    axisLine: {
      lineStyle: {
        width: 6,
        color: [
          [0.25, '#FF6B6B'],
          [0.5, '#FFE66D'],
          [0.75, '#4ECDC4'],
          [1, '#45B7D1']
        ]
      }
    },
    pointer: {
      icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
      length: '12%',
      width: 20,
      offsetCenter: [0, '-60%'],
      itemStyle: {
        color: 'auto'
      }
    },
    axisTick: {
      length: 12,
      lineStyle: {
        color: 'auto',
        width: 2
      }
    },
    splitLine: {
      length: 20,
      lineStyle: {
        color: 'auto',
        width: 5
      }
    },
    axisLabel: {
      color: '#464646',
      fontSize: 12,
      distance: -60,
      rotate: 'tangential',
      formatter: function (value: number) {
        if (value === 0) return '差';
        if (value === 25) return '一般';
        if (value === 50) return '良好';
        if (value === 75) return '优秀';
        if (value === 100) return '完美';
        return '';
      }
    },
    title: {
      offsetCenter: [0, '-10%'],
      fontSize: 16,
      fontWeight: 'bold',
      color: '#464646'
    },
    detail: {
      fontSize: 24,
      fontWeight: 'bold',
      offsetCenter: [0, '-35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return Math.round(value) + '%';
      },
      color: 'auto'
    },
    data: [{
      value: 85,
      name: '系统健康度'
    }]
  }]
}));

// 计算系统状态
const systemStatus = computed(() => {
  if (!props.healthData) {
    return { type: 'default', text: '未知', color: '#d9d9d9' };
  }

  const status = props.healthData.app_info.status;
  switch (status) {
    case 'healthy':
      return { type: 'success', text: '健康', color: '#52c41a' };
    case 'warning':
      return { type: 'warning', text: '警告', color: '#faad14' };
    case 'error':
      return { type: 'error', text: '错误', color: '#ff4d4f' };
    default:
      return { type: 'default', text: '未知', color: '#d9d9d9' };
  }
});

// 计算数据库状态
const dbStatus = computed(() => {
  if (!props.healthData?.database) {
    return { type: 'default', text: '未知', color: '#d9d9d9' };
  }

  const status = props.healthData.database.status;
  switch (status) {
    case 'connected':
      return { type: 'success', text: '已连接', color: '#52c41a' };
    case 'disconnected':
      return { type: 'error', text: '已断开', color: '#ff4d4f' };
    default:
      return { type: 'warning', text: '连接中', color: '#faad14' };
  }
});

// 计算运行时长
const uptimeText = computed(() => {
  if (!props.healthData?.app_info.uptime) return '0分钟';
  
  const uptime = props.healthData.app_info.uptime;
  const hours = Math.floor(uptime / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);
  
  if (hours > 24) {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}天${remainingHours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
});

// 计算健康分数
const healthScore = computed(() => {
  if (!props.healthData) return 0;
  
  let score = 0;
  
  // 系统状态权重 40%
  if (props.healthData.app_info.status === 'healthy') score += 40;
  else if (props.healthData.app_info.status === 'warning') score += 20;
  
  // 数据库状态权重 30%
  if (props.healthData.database.status === 'connected') score += 30;
  
  // STRM任务状态权重 20%
  if (props.healthData.strm_tasks.status === 'healthy') score += 20;
  else if (props.healthData.strm_tasks.status === 'warning') score += 10;
  
  // API性能状态权重 10%
  if (props.healthData.api_performance.status === 'healthy') score += 10;
  else if (props.healthData.api_performance.status === 'warning') score += 5;
  
  return score;
});

// 监听数据变化，更新图表
watch(() => props.healthData, () => {
  if (props.healthData) {
    updateHealthGauge(prev => ({
      ...prev,
      series: [{
        ...prev.series[0],
        data: [{
          value: healthScore.value,
          name: '系统健康度'
        }]
      }]
    }));
  }
}, { immediate: true });

function goToSystemDetail() {
  router.push('/monitor/system');
}
</script>

<template>
  <NCard class="enhanced-system-overview-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Icon icon="mdi:monitor-dashboard" class="text-2xl text-blue-500" />
          <span class="text-lg font-semibold">系统状态概览</span>
        </div>
        <NButton text @click="goToSystemDetail">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          查看详情
        </NButton>
      </div>
    </template>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 健康度仪表盘 -->
      <div class="health-gauge-container">
        <div ref="healthGaugeRef" class="health-gauge"></div>
      </div>

      <!-- 系统信息 -->
      <div class="system-info">
        <NGrid :cols="2" :x-gap="16" :y-gap="16">
          <!-- 系统状态 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:server" :style="{ color: systemStatus.color }" />
              </div>
              <div class="info-content">
                <div class="info-label">系统状态</div>
                <NTag :type="systemStatus.type" size="large">
                  {{ systemStatus.text }}
                </NTag>
              </div>
            </div>
          </NGridItem>

          <!-- 数据库状态 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:database" :style="{ color: dbStatus.color }" />
              </div>
              <div class="info-content">
                <div class="info-label">数据库</div>
                <NTag :type="dbStatus.type" size="large">
                  {{ dbStatus.text }}
                </NTag>
              </div>
            </div>
          </NGridItem>

          <!-- 运行时长 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:clock-outline" class="text-green-500" />
              </div>
              <div class="info-content">
                <div class="info-label">运行时长</div>
                <div class="info-value">{{ uptimeText }}</div>
              </div>
            </div>
          </NGridItem>

          <!-- 平台信息 -->
          <NGridItem>
            <div class="info-item">
              <div class="info-icon">
                <Icon icon="mdi:desktop-tower" class="text-purple-500" />
              </div>
              <div class="info-content">
                <div class="info-label">运行平台</div>
                <div class="info-value">{{ healthData?.app_info.platform || '未知' }}</div>
              </div>
            </div>
          </NGridItem>
        </NGrid>

        <!-- 版本信息 -->
        <div class="version-info mt-4 p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">应用版本</span>
            <span class="font-mono text-sm">{{ healthData?.app_info.version || 'v1.0.0' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToSystemDetail" type="primary" ghost>
          <template #icon>
            <Icon icon="mdi:chart-line" />
          </template>
          查看系统详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.enhanced-system-overview-card {
  height: 100%;
  transition: all 0.3s ease;
}

.enhanced-system-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.health-gauge-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.health-gauge {
  width: 100%;
  height: 200px;
}

.system-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.info-icon {
  font-size: 1.5rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 2px;
}

.info-value {
  font-weight: 600;
  color: #1e293b;
}

.version-info {
  border: 1px solid #e2e8f0;
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .health-gauge {
    height: 150px;
  }
}
</style>
